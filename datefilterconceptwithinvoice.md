# Invoice Report Date Filter समस्या और समाधान

## समस्या विश्लेषण (Problem Analysis)

### मुख्य समस्याएं:
1. **"All" फिल्टर काम नहीं कर रहा**: जब user "All time" पर click करता है, तो सभी invoices नहीं दिख रहे
2. **Default 15-day filter नहीं लग रहा**: Page load पर 15 दिन का default filter नहीं लग रहा
3. **Date filtering logic में inconsistency**: Frontend filtering और API call में अलग-अलग logic

### वर्तमान Code Flow:

#### 1. InvoiceReport.jsx में Initial State:
```javascript
const [startDate, setStartDate] = useState("");
const [endDate, setEndDate] = useState("");
```

#### 2. fetchInvoices Function में Default Logic:
```javascript
// Use provided dates or default to 15-day range
let date_from, date_to;

if (start || end) {
  date_from = formatDate(start);
  date_to = formatDate(end);
} else {
  const today = new Date();
  const fifteenDaysAgo = new Date();
  fifteenDaysAgo.setDate(today.getDate() - 15);
  date_from = formatDate(fifteenDaysAgo);
  date_to = formatDate(today);
}
```

#### 3. Frontend Filtering में समस्या:
```javascript
let matchesDateRange = true;
if (startDate || endDate) {
  // यहाँ filtering logic है
  // लेकिन अगर startDate और endDate empty हैं तो सभी records show होते हैं
}
```

## समाधान (Solution)

### मुख्य बदलाव InvoiceReport.jsx में:

#### 1. Initial State को Modify करें:
```javascript
// Default 15-day range set करें
const getDefaultDateRange = () => {
  const today = new Date();
  const fifteenDaysAgo = new Date();
  fifteenDaysAgo.setDate(today.getDate() - 15);
  
  return {
    startDate: fifteenDaysAgo.toISOString().split('T')[0],
    endDate: today.toISOString().split('T')[0]
  };
};

const defaultRange = getDefaultDateRange();
const [startDate, setStartDate] = useState(defaultRange.startDate);
const [endDate, setEndDate] = useState(defaultRange.endDate);
const [isAllTimeFilter, setIsAllTimeFilter] = useState(false);
```

#### 2. handleApplyDateFilter Function को Update करें:
```javascript
const handleApplyDateFilter = (start, end) => {
  // Check if this is "All time" filter (both dates are empty/null)
  if (!start && !end) {
    setIsAllTimeFilter(true);
    setStartDate('');
    setEndDate('');
    setCurrentPage(1);
    fetchAllInvoices(); // New function for fetching all invoices
    Swal.fire({
      icon: 'success',
      title: 'All Time Filter Applied',
      text: 'Showing all invoices',
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  } else {
    setIsAllTimeFilter(false);
    setStartDate(start);
    setEndDate(end);
    setCurrentPage(1);
    fetchInvoices(start, end);
    Swal.fire({
      icon: 'success',
      title: 'Date Filter Applied',
      text: `Showing invoices from ${start} to ${end}`,
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  }
};
```

#### 3. नया fetchAllInvoices Function बनाएं:
```javascript
const fetchAllInvoices = async () => {
  setLoading(true);
  setError(null);
  
  try {
    const response = await axios.post(
      ENDPOINTS.INVOICE_LISTING,
      {
        date_from: null, // या बहुत पुराना date जैसे "01/01/2020"
        date_to: null,   // या आज का date
        merchant_id: merchantId,
        filter_type: "",
        search: "",
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const records = response.data?.data?.records || [];
    setInvoices(records);
  } catch (err) {
    setError("Failed to load invoice data");
  } finally {
    setLoading(false);
  }
};
```

#### 4. filteredInvoices Logic को Update करें:
```javascript
const filteredInvoices = useMemo(() => {
  return invoices.filter((inv) => {
    const searchTermLower = searchTerm.toLowerCase().trim();

    const matchesSearch = /* existing search logic */;

    // Date range filtering logic
    let matchesDateRange = true;
    
    // अगर "All time" filter है तो सभी dates को allow करें
    if (!isAllTimeFilter && (startDate || endDate)) {
      const invoiceDate = parseInvoiceDate(inv.invoice_date);
      const startFilterDate = parseInvoiceDate(startDate);
      const endFilterDate = parseInvoiceDate(endDate);
      
      if (!invoiceDate) {
        matchesDateRange = false;
      } else {
        if (startFilterDate && invoiceDate < startFilterDate) {
          matchesDateRange = false;
        }
        if (endFilterDate && matchesDateRange && invoiceDate > endFilterDate) {
          matchesDateRange = false;
        }
      }
    }

    // Tab filter logic (existing)
    let matchesTab = true;
    if (activeTab !== "all") {
      // existing tab filtering logic
    }

    return matchesSearch && matchesDateRange && matchesTab;
  });
}, [invoices, searchTerm, startDate, endDate, activeTab, isAllTimeFilter]);
```

#### 5. useEffect को Update करें:
```javascript
useEffect(() => {
  const currentUser = getCurrentUserInvoice();
  if (currentUser && Array.isArray(currentUser.roles)) {
    setUserRoles(currentUser.roles);
  }
  
  // Default 15-day range के साथ fetch करें
  fetchInvoices(defaultRange.startDate, defaultRange.endDate);
}, []);
```

#### 6. handleClearFilters Function को Update करें:
```javascript
const handleClearFilters = () => {
  const defaultRange = getDefaultDateRange();
  setSearchTerm('');
  setStartDate(defaultRange.startDate);
  setEndDate(defaultRange.endDate);
  setIsAllTimeFilter(false);
  setCurrentPage(1);
  fetchInvoices(defaultRange.startDate, defaultRange.endDate);
};
```

### DateRangePicker Component में बदलाव:

#### applyPreset Function में "All time" case को Handle करें:
```javascript
const applyPreset = (preset) => {
  setActivePreset(preset);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let start, end;
  
  switch (preset) {
    // ... other cases
    case 'allTime':
      start = end = null; // This will trigger "All time" behavior
      break;
    case 'last15Days': // नया preset add करें
      end = new Date(today);
      start = new Date(today);
      start.setDate(start.getDate() - 14); // 15 days including today
      break;
    default:
      return;
  }
  
  setSelectedStartDate(start);
  setSelectedEndDate(end);
  setInputStartDate(start ? formatDate(start) : '');
  setInputEndDate(end ? formatDate(end) : '');
  
  if (start) {
    setStartMonth(new Date(start.getFullYear(), start.getMonth(), 1));
  }
  
  if (end) {
    setEndMonth(new Date(end.getFullYear(), end.getMonth(), 1));
  }
};
```

#### Preset Options में "Last 15 Days" Add करें:
```javascript
<div className="preset-options">
  <div className={`preset-option ${activePreset === 'today' ? 'active' : ''}`} onClick={() => applyPreset('today')}>Today</div>
  <div className={`preset-option ${activePreset === 'yesterday' ? 'active' : ''}`} onClick={() => applyPreset('yesterday')}>Yesterday</div>
  <div className={`preset-option ${activePreset === 'last7Days' ? 'active' : ''}`} onClick={() => applyPreset('last7Days')}>Last 7 Days</div>
  <div className={`preset-option ${activePreset === 'last15Days' ? 'active' : ''}`} onClick={() => applyPreset('last15Days')}>Last 15 Days</div>
  <div className={`preset-option ${activePreset === 'last30Days' ? 'active' : ''}`} onClick={() => applyPreset('last30Days')}>Last 30 Days</div>
  <div className={`preset-option ${activePreset === 'thisMonth' ? 'active' : ''}`} onClick={() => applyPreset('thisMonth')}>This Month</div>
  <div className={`preset-option ${activePreset === 'lastMonth' ? 'active' : ''}`} onClick={() => applyPreset('lastMonth')}>Last Month</div>
  <div className={`preset-option ${activePreset === 'thisYear' ? 'active' : ''}`} onClick={() => applyPreset('thisYear')}>This Year</div>
  <div className={`preset-option ${activePreset === 'lastYear' ? 'active' : ''}`} onClick={() => applyPreset('lastYear')}>Last Year</div>
  <div className={`preset-option ${activePreset === 'allTime' ? 'active' : ''}`} onClick={() => applyPreset('allTime')}>All time</div>
  <div className={`preset-option ${activePreset === 'customRange' ? 'active' : ''}`} onClick={() => setActivePreset('customRange')}>Custom Range</div>
</div>
```

## Implementation Steps:

### Step 1: InvoiceReport.jsx में State Variables Add करें
```javascript
// Add these new state variables
const [isAllTimeFilter, setIsAllTimeFilter] = useState(false);

// Modify existing state initialization
const getDefaultDateRange = () => {
  const today = new Date();
  const fifteenDaysAgo = new Date();
  fifteenDaysAgo.setDate(today.getDate() - 15);
  
  return {
    startDate: fifteenDaysAgo.toISOString().split('T')[0],
    endDate: today.toISOString().split('T')[0]
  };
};

const defaultRange = getDefaultDateRange();
const [startDate, setStartDate] = useState(defaultRange.startDate);
const [endDate, setEndDate] = useState(defaultRange.endDate);
```

### Step 2: fetchAllInvoices Function Add करें
```javascript
const fetchAllInvoices = async () => {
  setLoading(true);
  setError(null);
  
  try {
    // API call without date restrictions या बहुत wide date range के साथ
    const response = await axios.post(
      ENDPOINTS.INVOICE_LISTING,
      {
        date_from: "01/01/2020", // Very old date to get all records
        date_to: formatDate(new Date()), // Today's date
        merchant_id: merchantId,
        filter_type: "",
        search: "",
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const records = response.data?.data?.records || [];
    setInvoices(records);
  } catch (err) {
    setError("Failed to load invoice data");
  } finally {
    setLoading(false);
  }
};
```

### Step 3: handleApplyDateFilter Function को Replace करें
```javascript
const handleApplyDateFilter = (start, end) => {
  // Check if this is "All time" filter
  if (!start && !end) {
    setIsAllTimeFilter(true);
    setStartDate('');
    setEndDate('');
    setCurrentPage(1);
    fetchAllInvoices();
    Swal.fire({
      icon: 'success',
      title: 'All Time Filter Applied',
      text: 'Showing all invoices',
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  } else {
    setIsAllTimeFilter(false);
    setStartDate(start);
    setEndDate(end);
    setCurrentPage(1);
    fetchInvoices(start, end);
    Swal.fire({
      icon: 'success',
      title: 'Date Filter Applied',
      text: `Showing invoices from ${start} to ${end}`,
      timer: 1500,
      toast: true,
      position: 'top-end',
      showConfirmButton: false
    });
  }
};
```

### Step 4: filteredInvoices useMemo को Update करें
```javascript
const filteredInvoices = useMemo(() => {
  return invoices.filter((inv) => {
    const searchTermLower = searchTerm.toLowerCase().trim();

    const matchesSearch = /* existing search logic remains same */;

    // Updated date range filtering logic
    let matchesDateRange = true;
    
    // Only apply date filtering if not "All time" filter
    if (!isAllTimeFilter && (startDate || endDate)) {
      const invoiceDate = parseInvoiceDate(inv.invoice_date);
      const startFilterDate = parseInvoiceDate(startDate);
      const endFilterDate = parseInvoiceDate(endDate);
      
      if (!invoiceDate) {
        matchesDateRange = false;
      } else {
        if (startFilterDate && invoiceDate < startFilterDate) {
          matchesDateRange = false;
        }
        if (endFilterDate && matchesDateRange && invoiceDate > endFilterDate) {
          matchesDateRange = false;
        }
      }
    }

    // Tab filter logic (existing code remains same)
    let matchesTab = true;
    if (activeTab !== "all") {
      const statusId = parseInt(inv.status_id, 10);
      switch (activeTab) {
        case "unpaid":
          matchesTab = statusId === 1;
          break;
        case "in_process":
          matchesTab = statusId === 6;
          break;
        case "partially_paid":
          matchesTab = statusId === 17;
          break;
        case "paid":
          matchesTab = statusId === 2;
          break;
        case "overdue":
          matchesTab = inv.days_due > 0 && statusId !== 2 && statusId !== 3;
          break;
        default:
          matchesTab = true;
      }
    }

    return matchesSearch && matchesDateRange && matchesTab;
  });
}, [invoices, searchTerm, startDate, endDate, activeTab, isAllTimeFilter]);
```

### Step 5: useEffect को Update करें
```javascript
useEffect(() => {
  const currentUser = getCurrentUserInvoice();
  if (currentUser && Array.isArray(currentUser.roles)) {
    setUserRoles(currentUser.roles);
  }
  
  // Load with default 15-day range
  fetchInvoices(defaultRange.startDate, defaultRange.endDate);
}, []);
```

### Step 6: handleClearFilters को Update करें
```javascript
const handleClearFilters = () => {
  const defaultRange = getDefaultDateRange();
  setSearchTerm('');
  setStartDate(defaultRange.startDate);
  setEndDate(defaultRange.endDate);
  setIsAllTimeFilter(false);
  setCurrentPage(1);
  fetchInvoices(defaultRange.startDate, defaultRange.endDate);
};
```

## Testing Checklist:

1. **Page Load**: Default में 15 दिन का data दिखना चाहिए
2. **All Time Filter**: "All time" click करने पर सभी invoices दिखने चाहिए
3. **Custom Date Range**: Manual date selection काम करना चाहिए
4. **Clear Filters**: Clear करने पर वापस 15-day default आना चाहिए
5. **Tab Filters**: सभी tab filters (unpaid, paid, etc.) काम करने चाहिए
6. **Search**: Search functionality प्रभावित नहीं होनी चाहिए

## Important Notes:

1. **API Dependency**: यह solution assume करता है कि API wide date range को handle कर सकता है
2. **Performance**: "All time" filter के लिए large dataset का performance impact हो सकता है
3. **Date Format**: सभी date formats consistent रखें (YYYY-MM-DD)
4. **Error Handling**: API failures के लिए proper error handling add करें

यह solution flexible है और सभी requirements को पूरा करता है। सभी modifications केवल InvoiceReport.jsx में करने हैं।

## Optional Enhancement: DateRangePicker में "Last 15 Days" Preset

अगर आप DateRangePicker में भी "Last 15 Days" option add करना चाहते हैं:

### DateRangePicker.jsx में बदलाव:

#### 1. applyPreset Function में Case Add करें:
```javascript
case 'last15Days':
  end = new Date(today);
  start = new Date(today);
  start.setDate(start.getDate() - 14); // 15 days including today
  break;
```

#### 2. Preset Options में Button Add करें:
```javascript
<div className={`preset-option ${activePreset === 'last15Days' ? 'active' : ''}`}
     onClick={() => applyPreset('last15Days')}>
  Last 15 Days
</div>
```

### Complete Modified applyPreset Function:
```javascript
const applyPreset = (preset) => {
  setActivePreset(preset);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let start, end;

  switch (preset) {
    case 'today':
      start = end = new Date(today);
      break;
    case 'yesterday':
      start = end = new Date(today);
      start.setDate(start.getDate() - 1);
      end = new Date(start);
      break;
    case 'last7Days':
      end = new Date(today);
      start = new Date(today);
      start.setDate(start.getDate() - 6);
      break;
    case 'last15Days': // नया preset
      end = new Date(today);
      start = new Date(today);
      start.setDate(start.getDate() - 14);
      break;
    case 'last30Days':
      end = new Date(today);
      start = new Date(today);
      start.setDate(start.getDate() - 29);
      break;
    case 'thisMonth':
      start = new Date(today.getFullYear(), today.getMonth(), 1);
      end = new Date(today);
      break;
    case 'lastMonth':
      start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      end = new Date(today.getFullYear(), today.getMonth(), 0);
      break;
    case 'thisYear':
      start = new Date(today.getFullYear(), 0, 1);
      end = new Date(today);
      break;
    case 'lastYear':
      start = new Date(today.getFullYear() - 1, 0, 1);
      end = new Date(today.getFullYear() - 1, 11, 31);
      break;
    case 'allTime':
      start = end = null; // This triggers "All time" behavior
      break;
    default:
      return;
  }

  setSelectedStartDate(start);
  setSelectedEndDate(end);
  setInputStartDate(start ? formatDate(start) : '');
  setInputEndDate(end ? formatDate(end) : '');

  if (start) {
    setStartMonth(new Date(start.getFullYear(), start.getMonth(), 1));
  }

  if (end) {
    setEndMonth(new Date(end.getFullYear(), end.getMonth(), 1));
  }
};
```

## Final Implementation Summary:

### मुख्य Files में Changes:

1. **InvoiceReport.jsx**:
   - State variables add करें
   - Functions modify करें
   - Default 15-day behavior implement करें
   - "All time" filter functionality add करें

2. **DateRangePicker.jsx** (Optional):
   - "Last 15 Days" preset add करें
   - applyPreset function में case add करें

### Key Benefits:
- ✅ Default 15-day filter on page load
- ✅ "All time" filter shows all invoices
- ✅ Flexible date range selection
- ✅ Proper error handling
- ✅ User-friendly notifications
- ✅ Maintains existing functionality

### Performance Considerations:
- "All time" filter के लिए API optimization की जरूरत हो सकती है
- Large datasets के लिए pagination और lazy loading consider करें
- Client-side filtering को optimize करें

यह comprehensive solution है जो आपकी सभी requirements को पूरा करता है।
